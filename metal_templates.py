"""
Metal Genre Templates for SUNO AI Prompt Generation
Specialized templates and configurations for modern metal subgenres.
"""

from typing import Dict, List, Optional
from dataclasses import dataclass
import json


@dataclass
class MetalTemplate:
    """Template for a specific metal subgenre."""
    name: str
    description: str
    genre_tags: List[str]
    typical_instruments: List[str]
    vocal_styles: List[str]
    song_structure: List[str]
    mood_options: List[str]
    tempo_range: List[str]
    energy_range: List[str]
    characteristic_elements: List[str]
    example_themes: List[str]


class MetalTemplates:
    """Manager for metal genre templates and configurations."""
    
    def __init__(self):
        self.templates = self._initialize_templates()
        self.custom_templates = {}
        
    def _initialize_templates(self) -> Dict[str, MetalTemplate]:
        """Initialize built-in metal genre templates."""
        templates = {}
        
        # Modern Metalcore Template
        templates["Modern Metalcore"] = MetalTemplate(
            name="Modern Metalcore",
            description="Heavy, emotional metal with clean/screamed vocal contrast and melodic elements",
            genre_tags=["metalcore", "modern metal", "heavy", "melodic hardcore"],
            typical_instruments=[
                "Electric Guitar (Distorted)",
                "Electric Guitar (Clean)",
                "Bass Guitar (Heavy)",
                "Drums (Heavy)",
                "Synthesizer (Atmospheric)",
                "Strings (Orchestral)"
            ],
            vocal_styles=[
                "screamed vocals",
                "clean vocals",
                "melodic vocals",
                "harsh vocals",
                "emotional vocals"
            ],
            song_structure=[
                "Intro",
                "Verse",
                "Pre-Chorus",
                "Chorus",
                "Verse",
                "Chorus",
                "Bridge",
                "Breakdown",
                "Chorus",
                "Outro"
            ],
            mood_options=[
                "aggressive",
                "emotional",
                "uplifting",
                "dark",
                "melancholic",
                "triumphant",
                "intense"
            ],
            tempo_range=["Mid", "Fast"],
            energy_range=["Medium", "High", "Extreme"],
            characteristic_elements=[
                "heavy breakdowns",
                "melodic guitar leads",
                "emotional clean sections",
                "aggressive screamed vocals",
                "atmospheric interludes"
            ],
            example_themes=[
                "personal struggle",
                "overcoming adversity",
                "emotional pain",
                "relationships",
                "inner strength",
                "social issues"
            ]
        )
        
        # Djent Template
        templates["Djent"] = MetalTemplate(
            name="Djent",
            description="Progressive metal with polyrhythmic patterns and palm-muted guitar work",
            genre_tags=["djent", "progressive metal", "polyrhythmic", "technical"],
            typical_instruments=[
                "Electric Guitar (Palm Muted)",
                "Electric Guitar (7-String)",
                "Electric Guitar (8-String)",
                "Bass Guitar (Extended Range)",
                "Drums (Complex)",
                "Synthesizer (Ambient)",
                "Keyboard (Atmospheric)"
            ],
            vocal_styles=[
                "clean vocals",
                "harsh vocals",
                "ambient vocals",
                "progressive vocals",
                "melodic vocals"
            ],
            song_structure=[
                "Intro",
                "Verse",
                "Pre-Chorus",
                "Chorus",
                "Verse",
                "Chorus",
                "Instrumental Break",
                "Bridge",
                "Solo",
                "Chorus",
                "Outro"
            ],
            mood_options=[
                "atmospheric",
                "technical",
                "progressive",
                "introspective",
                "complex",
                "ambient",
                "rhythmic"
            ],
            tempo_range=["Mid", "Variable"],
            energy_range=["Medium", "High"],
            characteristic_elements=[
                "polyrhythmic patterns",
                "palm-muted riffs",
                "complex time signatures",
                "ambient textures",
                "technical precision"
            ],
            example_themes=[
                "existential questions",
                "consciousness",
                "technology",
                "space and time",
                "human condition",
                "philosophical concepts"
            ]
        )
        
        # Melodic Death Metal Template
        templates["Melodic Death Metal"] = MetalTemplate(
            name="Melodic Death Metal",
            description="Death metal with melodic guitar work and harmonic complexity",
            genre_tags=["melodic death metal", "death metal", "melodic", "Scandinavian metal"],
            typical_instruments=[
                "Electric Guitar (Melodic Lead)",
                "Electric Guitar (Rhythm)",
                "Electric Guitar (Harmonized)",
                "Bass Guitar (Aggressive)",
                "Drums (Blast Beats)",
                "Drums (Double Bass)",
                "Keyboard (Orchestral)",
                "Strings (Epic)"
            ],
            vocal_styles=[
                "death growls",
                "melodic clean vocals",
                "harsh vocals",
                "brutal vocals",
                "epic vocals"
            ],
            song_structure=[
                "Intro",
                "Verse",
                "Chorus",
                "Verse",
                "Chorus",
                "Guitar Solo",
                "Bridge",
                "Chorus",
                "Outro"
            ],
            mood_options=[
                "melodic",
                "aggressive",
                "epic",
                "dark",
                "triumphant",
                "majestic",
                "brutal"
            ],
            tempo_range=["Fast"],
            energy_range=["High", "Extreme"],
            characteristic_elements=[
                "melodic guitar harmonies",
                "blast beat drumming",
                "death growl vocals",
                "epic orchestrations",
                "technical solos"
            ],
            example_themes=[
                "mythology",
                "battles and warfare",
                "nature and seasons",
                "death and rebirth",
                "ancient history",
                "epic journeys"
            ]
        )
        
        # Progressive Metal Template
        templates["Progressive Metal"] = MetalTemplate(
            name="Progressive Metal",
            description="Complex, technical metal with intricate compositions and varied dynamics",
            genre_tags=["progressive metal", "progressive", "complex", "technical"],
            typical_instruments=[
                "Electric Guitar (Technical)",
                "Electric Guitar (Clean)",
                "Bass Guitar (Complex)",
                "Drums (Technical)",
                "Keyboard (Progressive)",
                "Synthesizer (Atmospheric)",
                "Piano (Classical)"
            ],
            vocal_styles=[
                "progressive vocals",
                "clean vocals",
                "operatic vocals",
                "melodic vocals",
                "dynamic vocals"
            ],
            song_structure=[
                "Intro",
                "Verse",
                "Chorus",
                "Verse",
                "Chorus",
                "Instrumental Section",
                "Bridge",
                "Guitar Solo",
                "Keyboard Solo",
                "Chorus",
                "Extended Outro"
            ],
            mood_options=[
                "progressive",
                "complex",
                "atmospheric",
                "epic",
                "introspective",
                "dynamic",
                "cerebral"
            ],
            tempo_range=["Variable"],
            energy_range=["Medium", "High"],
            characteristic_elements=[
                "complex time signatures",
                "extended compositions",
                "dynamic changes",
                "technical proficiency",
                "conceptual themes"
            ],
            example_themes=[
                "concept albums",
                "science fiction",
                "psychology",
                "spirituality",
                "social commentary",
                "abstract concepts"
            ]
        )
        
        # Technical Death Metal Template
        templates["Technical Death Metal"] = MetalTemplate(
            name="Technical Death Metal",
            description="Highly technical death metal with complex arrangements and virtuosic playing",
            genre_tags=["technical death metal", "death metal", "technical", "brutal"],
            typical_instruments=[
                "Electric Guitar (Technical)",
                "Electric Guitar (Sweep Picking)",
                "Electric Guitar (Tapping)",
                "Bass Guitar (Technical)",
                "Bass Guitar (Fretless)",
                "Drums (Technical)",
                "Drums (Blast Beats)"
            ],
            vocal_styles=[
                "death growls",
                "technical vocals",
                "brutal vocals",
                "guttural vocals",
                "varied harsh vocals"
            ],
            song_structure=[
                "Intro",
                "Verse",
                "Chorus",
                "Verse",
                "Chorus",
                "Technical Solo",
                "Bridge",
                "Bass Solo",
                "Chorus",
                "Outro"
            ],
            mood_options=[
                "technical",
                "aggressive",
                "complex",
                "brutal",
                "intense",
                "precise",
                "chaotic"
            ],
            tempo_range=["Fast"],
            energy_range=["High", "Extreme"],
            characteristic_elements=[
                "technical virtuosity",
                "complex arrangements",
                "unusual time signatures",
                "advanced techniques",
                "precision playing"
            ],
            example_themes=[
                "technical mastery",
                "chaos theory",
                "mathematical concepts",
                "scientific themes",
                "abstract brutality",
                "intellectual aggression"
            ]
        )
        
        return templates
    
    def get_template(self, genre_name: str) -> Optional[MetalTemplate]:
        """Get a template by genre name."""
        return self.templates.get(genre_name) or self.custom_templates.get(genre_name)
    
    def get_all_templates(self) -> Dict[str, MetalTemplate]:
        """Get all available templates."""
        all_templates = self.templates.copy()
        all_templates.update(self.custom_templates)
        return all_templates
    
    def get_genre_names(self) -> List[str]:
        """Get list of all available genre names."""
        return list(self.get_all_templates().keys())
    
    def add_custom_template(self, template: MetalTemplate) -> bool:
        """Add a custom template."""
        try:
            self.custom_templates[template.name] = template
            return True
        except Exception:
            return False
    
    def remove_custom_template(self, genre_name: str) -> bool:
        """Remove a custom template."""
        if genre_name in self.custom_templates:
            del self.custom_templates[genre_name]
            return True
        return False
    
    def save_custom_templates(self, filepath: str) -> bool:
        """Save custom templates to a JSON file."""
        try:
            templates_data = {}
            for name, template in self.custom_templates.items():
                templates_data[name] = {
                    "name": template.name,
                    "description": template.description,
                    "genre_tags": template.genre_tags,
                    "typical_instruments": template.typical_instruments,
                    "vocal_styles": template.vocal_styles,
                    "song_structure": template.song_structure,
                    "mood_options": template.mood_options,
                    "tempo_range": template.tempo_range,
                    "energy_range": template.energy_range,
                    "characteristic_elements": template.characteristic_elements,
                    "example_themes": template.example_themes
                }
            
            with open(filepath, 'w') as f:
                json.dump(templates_data, f, indent=2)
            return True
        except Exception:
            return False
    
    def load_custom_templates(self, filepath: str) -> bool:
        """Load custom templates from a JSON file."""
        try:
            with open(filepath, 'r') as f:
                templates_data = json.load(f)
            
            for name, data in templates_data.items():
                template = MetalTemplate(**data)
                self.custom_templates[name] = template
            return True
        except Exception:
            return False
