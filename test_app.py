#!/usr/bin/env python3
"""
Test script for SUNO AI Prompt Generator
Verifies core functionality without GUI dependencies.
"""

import sys
import os

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from prompt_generator import PromptGenerator
from metal_templates import MetalTemplates
from lyric_generator import LyricGenerator


def test_prompt_generator():
    """Test the prompt generator functionality."""
    print("Testing Prompt Generator...")
    
    generator = PromptGenerator()
    
    # Test basic generation
    try:
        prompt = generator.generate("aggressive breakup song", "Modern Metalcore")
        print("✓ Basic prompt generation successful")
        print(f"Generated prompt length: {len(prompt)} characters")
    except Exception as e:
        print(f"✗ Basic prompt generation failed: {e}")
        return False
    
    # Test advanced generation
    try:
        prompt = generator.generate_advanced(
            "epic battle song", 
            "Melodic Death Metal", 
            "Battle Hymn",
            "",
            "High",
            "Fast",
            "Mixed Vocals"
        )
        print("✓ Advanced prompt generation successful")
    except Exception as e:
        print(f"✗ Advanced prompt generation failed: {e}")
        return False
    
    return True


def test_metal_templates():
    """Test the metal templates functionality."""
    print("\nTesting Metal Templates...")
    
    templates = MetalTemplates()
    
    # Test template retrieval
    try:
        template = templates.get_template("Modern Metalcore")
        if template and template.name == "Modern Metalcore":
            print("✓ Template retrieval successful")
        else:
            print("✗ Template retrieval failed")
            return False
    except Exception as e:
        print(f"✗ Template retrieval failed: {e}")
        return False
    
    # Test all templates
    try:
        all_templates = templates.get_all_templates()
        expected_genres = ["Modern Metalcore", "Djent", "Melodic Death Metal", 
                          "Progressive Metal", "Technical Death Metal"]
        
        for genre in expected_genres:
            if genre not in all_templates:
                print(f"✗ Missing template: {genre}")
                return False
        
        print(f"✓ All {len(expected_genres)} templates available")
    except Exception as e:
        print(f"✗ Template enumeration failed: {e}")
        return False
    
    return True


def test_lyric_generator():
    """Test the lyric generator functionality."""
    print("\nTesting Lyric Generator...")
    
    generator = LyricGenerator()
    
    # Test theme analysis
    try:
        theme = generator.analyze_theme("angry breakup song about betrayal")
        if theme in ["anger", "betrayal", "loss"]:
            print(f"✓ Theme analysis successful: {theme}")
        else:
            print(f"✓ Theme analysis returned: {theme} (acceptable)")
    except Exception as e:
        print(f"✗ Theme analysis failed: {e}")
        return False
    
    # Test lyric generation
    try:
        lyrics = generator.generate_song_lyrics("epic battle", "Melodic Death Metal")
        if lyrics and lyrics.title and lyrics.sections:
            print(f"✓ Lyric generation successful: '{lyrics.title}' with {len(lyrics.sections)} sections")
        else:
            print("✗ Lyric generation failed - empty result")
            return False
    except Exception as e:
        print(f"✗ Lyric generation failed: {e}")
        return False
    
    # Test SUNO formatting
    try:
        formatted = generator.format_lyrics_for_suno(lyrics)
        if "[Verse]" in formatted and "[Chorus]" in formatted:
            print("✓ SUNO formatting successful")
        else:
            print("✗ SUNO formatting failed - missing structure tags")
            return False
    except Exception as e:
        print(f"✗ SUNO formatting failed: {e}")
        return False
    
    return True


def test_integration():
    """Test integration between components."""
    print("\nTesting Integration...")
    
    try:
        # Create components
        prompt_gen = PromptGenerator()
        lyric_gen = LyricGenerator()
        templates = MetalTemplates()
        
        # Generate lyrics
        song_lyrics = lyric_gen.generate_song_lyrics("dark emotional song", "Modern Metalcore")
        formatted_lyrics = lyric_gen.format_lyrics_for_suno(song_lyrics)
        
        # Generate prompt with lyrics
        prompt = prompt_gen.generate_advanced(
            "dark emotional song",
            "Modern Metalcore",
            song_lyrics.title,
            formatted_lyrics,
            "High",
            "Mid",
            "Mixed Vocals"
        )
        
        # Verify integration
        if song_lyrics.title in prompt and "[Verse]" in prompt and "[Chorus]" in prompt:
            print("✓ Component integration successful")
            return True
        else:
            print("✗ Component integration failed - missing expected content")
            return False
            
    except Exception as e:
        print(f"✗ Integration test failed: {e}")
        return False


def run_example():
    """Run a complete example to demonstrate functionality."""
    print("\n" + "="*60)
    print("EXAMPLE: Generating a complete SUNO AI prompt")
    print("="*60)
    
    try:
        # Initialize components
        prompt_gen = PromptGenerator()
        lyric_gen = LyricGenerator()
        
        # User input simulation
        description = "aggressive metalcore song about overcoming personal struggles"
        genre = "Modern Metalcore"
        
        print(f"Input Description: {description}")
        print(f"Selected Genre: {genre}")
        print()
        
        # Generate lyrics
        print("Generating lyrics...")
        song_lyrics = lyric_gen.generate_song_lyrics(description, genre)
        formatted_lyrics = lyric_gen.format_lyrics_for_suno(song_lyrics)
        
        print(f"Generated Title: {song_lyrics.title}")
        print(f"Detected Theme: {song_lyrics.theme}")
        print()
        
        # Generate complete prompt
        print("Generating SUNO AI prompt...")
        prompt = prompt_gen.generate_advanced(
            description,
            genre,
            song_lyrics.title,
            formatted_lyrics,
            "High",
            "Fast",
            "Mixed Vocals"
        )
        
        print("Generated Prompt:")
        print("-" * 40)
        print(prompt)
        print("-" * 40)
        print()
        print("✓ Example completed successfully!")
        
    except Exception as e:
        print(f"✗ Example failed: {e}")


def main():
    """Run all tests."""
    print("SUNO AI Prompt Generator - Test Suite")
    print("=" * 50)
    
    tests = [
        test_prompt_generator,
        test_metal_templates,
        test_lyric_generator,
        test_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\nTest Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! The application is ready to use.")
        run_example()
    else:
        print("✗ Some tests failed. Please check the implementation.")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
