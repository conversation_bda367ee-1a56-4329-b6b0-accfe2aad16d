# Example SUNO AI Prompts Generated by Metal Prompt Generator

This file contains example prompts generated by the application to demonstrate its capabilities across different metal genres.

## Modern Metalcore Examples

### Example 1: Emotional Struggle
**Input:** "Song about overcoming depression and finding inner strength"
**Genre:** Modern Metalcore

```
# SUNO AI Prompt - Generated by Metal Prompt Generator
# Title: Inner Strength
# Description: Song about overcoming depression and finding inner strength

[Intro]
[Genre: metalcore]
[Genre: modern metal]
[Genre: heavy]
[Mood: emotional]
[Energy: High]
[Tempo: Mid]
[Instrument: Electric Guitar (Distorted)]
[Instrument: Electric Guitar (Clean)]
[Instrument: Bass Guitar (Heavy)]
[Instrument: Drums (Heavy)]
[Vocalist: screamed vocals]

[Verse]
In the depths of darkness
The steel of sorrow will rise
When pain meets hope, we endure
No despair can stop this strength

[Chorus]
Rise above the darkness
With fire in our hearts
The light will set us free
Rise above the darkness

[Bridge]
In the silence before the storm
The mountain whispers of freedom
We fight through destiny
Like thunder in the eternity
```

### Example 2: Aggressive Breakup
**Input:** "Angry song about betrayal and moving on"
**Genre:** Modern Metalcore

```
# SUNO AI Prompt - Generated by Metal Prompt Generator
# Title: Betrayal Fire
# Description: Angry song about betrayal and moving on

[Intro]
[Genre: metalcore]
[Genre: modern metal]
[Genre: heavy]
[Mood: aggressive]
[Energy: Extreme]
[Tempo: Fast]
[Instrument: Electric Guitar (Distorted)]
[Instrument: Bass Guitar (Heavy)]
[Instrument: Drums (Heavy)]
[Instrument: Synthesizer (Atmospheric)]
[Vocalist: harsh vocals]

[Verse]
Fire burns within my veins
The thunder of rage will destroy
Through fury we break like steel
No trust can stop this anger

[Chorus]
Burn it all to the ground
With rage in our hearts
The fury will set us free
Burn it all to the ground

[Breakdown]
[Instrument: Electric Guitar (Palm Muted)]
[Energy: Extreme]
```

## Djent Examples

### Example 3: Technical Complexity
**Input:** "Complex instrumental track with polyrhythmic patterns"
**Genre:** Djent

```
# SUNO AI Prompt - Generated by Metal Prompt Generator
# Title: Polyrhythmic Void
# Description: Complex instrumental track with polyrhythmic patterns

[Intro]
[Genre: djent]
[Genre: progressive metal]
[Genre: polyrhythmic]
[Mood: technical]
[Energy: High]
[Tempo: Variable]
[Instrument: Electric Guitar (Palm Muted)]
[Instrument: Electric Guitar (7-String)]
[Instrument: Bass Guitar (Extended Range)]
[Instrument: Drums (Complex)]
[Vocalist: instrumental]

[Verse]
[Instrument: Electric Guitar (Technical)]
[Time Signature: 7/8]

[Chorus]
[Instrument: Synthesizer (Ambient)]
[Time Signature: 4/4]

[Bridge]
[Instrument: Electric Guitar (Polyrhythmic)]
[Time Signature: 5/4]
```

## Melodic Death Metal Examples

### Example 4: Epic Battle Theme
**Input:** "Epic song about ancient warriors and glorious battle"
**Genre:** Melodic Death Metal

```
# SUNO AI Prompt - Generated by Metal Prompt Generator
# Title: Warriors of Eternity
# Description: Epic song about ancient warriors and glorious battle

[Intro]
[Genre: melodic death metal]
[Genre: death metal]
[Genre: melodic]
[Mood: epic]
[Energy: Extreme]
[Tempo: Fast]
[Instrument: Electric Guitar (Melodic Lead)]
[Instrument: Electric Guitar (Harmonized)]
[Instrument: Bass Guitar (Aggressive)]
[Instrument: Drums (Blast Beats)]
[Instrument: Keyboard (Orchestral)]
[Vocalist: death growls]

[Verse]
Through the darkness comes the light
The steel of power will conquer
When fire meets victory, we triumph
No defeat can stop this might

[Chorus]
Warriors of eternity
With steel in our hearts
The fire will set us free
Warriors of eternity

[Guitar Solo]
[Instrument: Electric Guitar (Melodic Lead)]
[Instrument: Electric Guitar (Harmonized)]

[Bridge]
In the heart of darkness
The mountain whispers of destiny
We fight through freedom
Like storm in the eternity
```

## Progressive Metal Examples

### Example 5: Conceptual Journey
**Input:** "Progressive song about space exploration and human consciousness"
**Genre:** Progressive Metal

```
# SUNO AI Prompt - Generated by Metal Prompt Generator
# Title: Cosmic Consciousness
# Description: Progressive song about space exploration and human consciousness

[Intro]
[Genre: progressive metal]
[Genre: progressive]
[Genre: complex]
[Mood: atmospheric]
[Energy: Medium]
[Tempo: Variable]
[Instrument: Electric Guitar (Technical)]
[Instrument: Bass Guitar (Complex)]
[Instrument: Drums (Technical)]
[Instrument: Keyboard (Progressive)]
[Instrument: Synthesizer (Atmospheric)]
[Vocalist: progressive vocals]

[Verse]
Beyond the veil of time
The ocean whispers of infinity
We rise through consciousness
Like star in the void

[Chorus]
Light conquers all
Through eternity we find our way
The infinity will set us free
Light conquers all

[Instrumental Section]
[Time Signature: 7/8]
[Instrument: Keyboard (Progressive)]
[Instrument: Electric Guitar (Technical)]

[Bridge]
At the edge of eternity
The forest whispers of truth
We create through journey
Like wind in the dream
```

## Technical Death Metal Examples

### Example 6: Brutal Precision
**Input:** "Extremely technical and brutal instrumental showcase"
**Genre:** Technical Death Metal

```
# SUNO AI Prompt - Generated by Metal Prompt Generator
# Title: Technical Brutality
# Description: Extremely technical and brutal instrumental showcase

[Intro]
[Genre: technical death metal]
[Genre: death metal]
[Genre: technical]
[Mood: brutal]
[Energy: Extreme]
[Tempo: Fast]
[Instrument: Electric Guitar (Technical)]
[Instrument: Electric Guitar (Sweep Picking)]
[Instrument: Bass Guitar (Technical)]
[Instrument: Drums (Technical)]
[Vocalist: instrumental]

[Verse]
[Time Signature: 11/8]
[Instrument: Electric Guitar (Tapping)]

[Chorus]
[Time Signature: 4/4]
[Instrument: Electric Guitar (Sweep Picking)]

[Technical Solo]
[Instrument: Electric Guitar (Technical)]
[Instrument: Bass Guitar (Technical)]
[Time Signature: 7/8]

[Bridge]
[Time Signature: 5/4]
[Instrument: Drums (Technical)]
```

## Usage Tips

1. **Copy the entire prompt** including meta tags for best results
2. **Paste directly into SUNO AI** custom lyrics mode
3. **Adjust energy and tempo** tags based on your preference
4. **Experiment with different combinations** of the generated elements
5. **Use the breakdown sections** for maximum impact in metalcore tracks

## Customization Notes

- Replace placeholder lyrics with your own content
- Adjust time signatures for more complexity
- Add or remove instrument tags based on your vision
- Modify vocal styles to match your preference
- Combine elements from different examples for unique results
