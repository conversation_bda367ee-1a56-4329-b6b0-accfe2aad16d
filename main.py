#!/usr/bin/env python3
"""
SUNO AI Prompt Generator - Main Application
A Python GUI application for generating complex SUNO AI prompts for modern metal genres.

Author: AI Assistant
Version: 1.0.0
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import os
from datetime import datetime
from typing import Dict, List, Optional

from prompt_generator import PromptGenerator
from metal_templates import MetalTemplates
from lyric_generator import LyricGenerator


class SunoPromptApp:
    """Main application class for the SUNO AI Prompt Generator."""
    
    def __init__(self, root):
        self.root = root
        self.root.title("SUNO AI Prompt Generator - Metal Edition")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)
        
        # Initialize components
        self.prompt_generator = PromptGenerator()
        self.metal_templates = MetalTemplates()
        self.lyric_generator = LyricGenerator()
        
        # Application state
        self.current_prompt = ""
        self.saved_prompts = []
        
        # Setup UI
        self.setup_styles()
        self.create_widgets()
        self.load_saved_prompts()
        
    def setup_styles(self):
        """Configure custom styles for the application."""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Custom colors for metal theme
        style.configure('Metal.TFrame', background='#2b2b2b')
        style.configure('Metal.TLabel', background='#2b2b2b', foreground='#ffffff')
        style.configure('Metal.TButton', background='#404040', foreground='#ffffff')
        style.map('Metal.TButton', background=[('active', '#505050')])
        
    def create_widgets(self):
        """Create and layout all GUI widgets."""
        # Main container
        main_frame = ttk.Frame(self.root, style='Metal.TFrame')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Title
        title_label = ttk.Label(
            main_frame, 
            text="SUNO AI Prompt Generator - Metal Edition",
            font=('Arial', 16, 'bold'),
            style='Metal.TLabel'
        )
        title_label.pack(pady=(0, 20))
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # Create tabs
        self.create_input_tab()
        self.create_templates_tab()
        self.create_preview_tab()
        self.create_saved_tab()
        
        # Bottom frame for main actions
        bottom_frame = ttk.Frame(main_frame, style='Metal.TFrame')
        bottom_frame.pack(fill=tk.X, pady=(10, 0))
        
        # Main action buttons
        ttk.Button(
            bottom_frame,
            text="Generate Prompt",
            command=self.generate_prompt,
            style='Metal.TButton'
        ).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(
            bottom_frame,
            text="Save Prompt",
            command=self.save_prompt,
            style='Metal.TButton'
        ).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(
            bottom_frame,
            text="Export",
            command=self.export_prompt,
            style='Metal.TButton'
        ).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(
            bottom_frame,
            text="Clear All",
            command=self.clear_all,
            style='Metal.TButton'
        ).pack(side=tk.RIGHT)
        
    def create_input_tab(self):
        """Create the main input tab."""
        input_frame = ttk.Frame(self.notebook, style='Metal.TFrame')
        self.notebook.add(input_frame, text="Input")

        # Scrollable frame
        canvas = tk.Canvas(input_frame, bg='#2b2b2b')
        scrollbar = ttk.Scrollbar(input_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas, style='Metal.TFrame')

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Basic input section
        basic_frame = ttk.LabelFrame(scrollable_frame, text="Basic Song Concept", style='Metal.TFrame')
        basic_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(basic_frame, text="Song Description:", style='Metal.TLabel').pack(anchor=tk.W, padx=5, pady=2)
        self.song_description = tk.Text(basic_frame, height=3, bg='#404040', fg='white', insertbackground='white')
        self.song_description.pack(fill=tk.X, padx=5, pady=2)

        # Song title
        ttk.Label(basic_frame, text="Song Title (optional):", style='Metal.TLabel').pack(anchor=tk.W, padx=5, pady=(10,2))
        self.song_title = tk.Entry(basic_frame, bg='#404040', fg='white', insertbackground='white')
        self.song_title.pack(fill=tk.X, padx=5, pady=2)

        # Genre selection
        genre_frame = ttk.LabelFrame(scrollable_frame, text="Metal Genre", style='Metal.TFrame')
        genre_frame.pack(fill=tk.X, padx=10, pady=10)

        self.genre_var = tk.StringVar(value="Modern Metalcore")
        genres = ["Modern Metalcore", "Djent", "Melodic Death Metal", "Progressive Metal", "Technical Death Metal"]

        for genre in genres:
            ttk.Radiobutton(
                genre_frame,
                text=genre,
                variable=self.genre_var,
                value=genre,
                command=self.on_genre_change
            ).pack(anchor=tk.W, padx=5, pady=2)

        # Advanced options
        advanced_frame = ttk.LabelFrame(scrollable_frame, text="Advanced Options", style='Metal.TFrame')
        advanced_frame.pack(fill=tk.X, padx=10, pady=10)

        # Energy level
        energy_frame = ttk.Frame(advanced_frame, style='Metal.TFrame')
        energy_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(energy_frame, text="Energy Level:", style='Metal.TLabel').pack(side=tk.LEFT)
        self.energy_var = tk.StringVar(value="Auto")
        energy_combo = ttk.Combobox(energy_frame, textvariable=self.energy_var,
                                   values=["Auto", "Low", "Medium", "High", "Extreme"], state="readonly")
        energy_combo.pack(side=tk.RIGHT, padx=(10, 0))

        # Tempo
        tempo_frame = ttk.Frame(advanced_frame, style='Metal.TFrame')
        tempo_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(tempo_frame, text="Tempo:", style='Metal.TLabel').pack(side=tk.LEFT)
        self.tempo_var = tk.StringVar(value="Auto")
        tempo_combo = ttk.Combobox(tempo_frame, textvariable=self.tempo_var,
                                  values=["Auto", "Slow", "Mid", "Fast", "Variable"], state="readonly")
        tempo_combo.pack(side=tk.RIGHT, padx=(10, 0))

        # Vocal style
        vocal_frame = ttk.Frame(advanced_frame, style='Metal.TFrame')
        vocal_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(vocal_frame, text="Vocal Style:", style='Metal.TLabel').pack(side=tk.LEFT)
        self.vocal_var = tk.StringVar(value="Auto")
        vocal_combo = ttk.Combobox(vocal_frame, textvariable=self.vocal_var,
                                  values=["Auto", "Clean Vocals", "Harsh Vocals", "Mixed Vocals", "Instrumental"], state="readonly")
        vocal_combo.pack(side=tk.RIGHT, padx=(10, 0))

        # Lyric options
        lyric_frame = ttk.LabelFrame(scrollable_frame, text="Lyric Options", style='Metal.TFrame')
        lyric_frame.pack(fill=tk.X, padx=10, pady=10)

        self.generate_lyrics_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(lyric_frame, text="Generate lyrics automatically",
                       variable=self.generate_lyrics_var, command=self.on_lyrics_option_change).pack(anchor=tk.W, padx=5, pady=2)

        self.custom_lyrics_frame = ttk.Frame(lyric_frame, style='Metal.TFrame')
        self.custom_lyrics_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(self.custom_lyrics_frame, text="Custom Lyrics:", style='Metal.TLabel').pack(anchor=tk.W)
        self.custom_lyrics = tk.Text(self.custom_lyrics_frame, height=6, bg='#404040', fg='white', insertbackground='white')
        self.custom_lyrics.pack(fill=tk.X, pady=2)

        # Pack canvas and scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
    def create_templates_tab(self):
        """Create the templates management tab."""
        templates_frame = ttk.Frame(self.notebook, style='Metal.TFrame')
        self.notebook.add(templates_frame, text="Templates")

        # Template selection
        selection_frame = ttk.LabelFrame(templates_frame, text="Genre Templates", style='Metal.TFrame')
        selection_frame.pack(fill=tk.X, padx=10, pady=10)

        # Template listbox
        template_list_frame = ttk.Frame(selection_frame, style='Metal.TFrame')
        template_list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.template_listbox = tk.Listbox(template_list_frame, bg='#404040', fg='white', selectbackground='#606060')
        template_scrollbar = ttk.Scrollbar(template_list_frame, orient="vertical", command=self.template_listbox.yview)
        self.template_listbox.configure(yscrollcommand=template_scrollbar.set)

        self.template_listbox.pack(side="left", fill="both", expand=True)
        template_scrollbar.pack(side="right", fill="y")

        # Populate template list
        self.refresh_template_list()

        # Template details
        details_frame = ttk.LabelFrame(templates_frame, text="Template Details", style='Metal.TFrame')
        details_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        self.template_details = tk.Text(details_frame, height=15, bg='#404040', fg='white',
                                       insertbackground='white', wrap=tk.WORD, state=tk.DISABLED)
        details_scrollbar = ttk.Scrollbar(details_frame, orient="vertical", command=self.template_details.yview)
        self.template_details.configure(yscrollcommand=details_scrollbar.set)

        self.template_details.pack(side="left", fill="both", expand=True, padx=5, pady=5)
        details_scrollbar.pack(side="right", fill="y")

        # Template controls
        controls_frame = ttk.Frame(templates_frame, style='Metal.TFrame')
        controls_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(controls_frame, text="Use Template", command=self.use_template, style='Metal.TButton').pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(controls_frame, text="Refresh", command=self.refresh_template_list, style='Metal.TButton').pack(side=tk.LEFT)

        # Bind selection event
        self.template_listbox.bind('<<ListboxSelect>>', self.on_template_select)
        
    def create_preview_tab(self):
        """Create the prompt preview tab."""
        preview_frame = ttk.Frame(self.notebook, style='Metal.TFrame')
        self.notebook.add(preview_frame, text="Preview")
        
        ttk.Label(preview_frame, text="Generated Prompt Preview:", style='Metal.TLabel').pack(anchor=tk.W, padx=10, pady=5)
        
        self.preview_text = tk.Text(
            preview_frame,
            height=20,
            bg='#404040',
            fg='white',
            insertbackground='white',
            wrap=tk.WORD
        )
        self.preview_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Preview controls
        preview_controls = ttk.Frame(preview_frame, style='Metal.TFrame')
        preview_controls.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(
            preview_controls,
            text="Copy to Clipboard",
            command=self.copy_to_clipboard,
            style='Metal.TButton'
        ).pack(side=tk.LEFT, padx=(0, 10))
        
    def create_saved_tab(self):
        """Create the saved prompts tab."""
        saved_frame = ttk.Frame(self.notebook, style='Metal.TFrame')
        self.notebook.add(saved_frame, text="Saved Prompts")

        # Saved prompts list
        list_frame = ttk.LabelFrame(saved_frame, text="Saved Prompts", style='Metal.TFrame')
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create treeview for saved prompts
        columns = ('Title', 'Genre', 'Date')
        self.saved_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=10)

        # Define headings
        self.saved_tree.heading('Title', text='Title')
        self.saved_tree.heading('Genre', text='Genre')
        self.saved_tree.heading('Date', text='Date')

        # Configure column widths
        self.saved_tree.column('Title', width=200)
        self.saved_tree.column('Genre', width=150)
        self.saved_tree.column('Date', width=150)

        # Scrollbar for treeview
        saved_scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.saved_tree.yview)
        self.saved_tree.configure(yscrollcommand=saved_scrollbar.set)

        self.saved_tree.pack(side="left", fill="both", expand=True, padx=5, pady=5)
        saved_scrollbar.pack(side="right", fill="y")

        # Controls
        controls_frame = ttk.Frame(saved_frame, style='Metal.TFrame')
        controls_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(controls_frame, text="Load", command=self.load_selected_prompt, style='Metal.TButton').pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(controls_frame, text="Delete", command=self.delete_selected_prompt, style='Metal.TButton').pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(controls_frame, text="Load from File", command=self.load_prompt_from_file, style='Metal.TButton').pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(controls_frame, text="Refresh", command=self.refresh_saved_list, style='Metal.TButton').pack(side=tk.RIGHT)

        # Populate saved prompts
        self.refresh_saved_list()
        
    def on_genre_change(self):
        """Handle genre selection change."""
        selected_genre = self.genre_var.get()
        template = self.metal_templates.get_template(selected_genre)

        if template:
            # Update advanced options based on genre
            if self.energy_var.get() == "Auto":
                default_energy = template.energy_range[0] if template.energy_range else "Medium"
                # Don't change the display, but store the default

            if self.tempo_var.get() == "Auto":
                default_tempo = template.tempo_range[0] if template.tempo_range else "Mid"
                # Don't change the display, but store the default

    def on_lyrics_option_change(self):
        """Handle lyrics generation option change."""
        if self.generate_lyrics_var.get():
            # Hide custom lyrics input
            self.custom_lyrics_frame.pack_forget()
        else:
            # Show custom lyrics input
            self.custom_lyrics_frame.pack(fill=tk.X, padx=5, pady=5)

    def generate_prompt(self):
        """Generate a SUNO AI prompt based on current inputs."""
        try:
            description = self.song_description.get("1.0", tk.END).strip()
            genre = self.genre_var.get()
            title = self.song_title.get().strip()

            if not description:
                messagebox.showwarning("Warning", "Please enter a song description.")
                return

            # Get advanced options
            energy = self.energy_var.get() if self.energy_var.get() != "Auto" else None
            tempo = self.tempo_var.get() if self.tempo_var.get() != "Auto" else None
            vocal_style = self.vocal_var.get() if self.vocal_var.get() != "Auto" else None

            # Generate lyrics if requested
            lyrics = ""
            if self.generate_lyrics_var.get():
                song_lyrics = self.lyric_generator.generate_song_lyrics(description, genre, title)
                lyrics = self.lyric_generator.format_lyrics_for_suno(song_lyrics)
                if not title:
                    title = song_lyrics.title
            else:
                lyrics = self.custom_lyrics.get("1.0", tk.END).strip()

            # Generate the prompt with advanced options
            prompt = self.prompt_generator.generate_advanced(
                description, genre, title, lyrics, energy, tempo, vocal_style
            )
            self.current_prompt = prompt

            # Update preview
            self.preview_text.delete("1.0", tk.END)
            self.preview_text.insert("1.0", prompt)

            # Switch to preview tab
            self.notebook.select(2)  # Preview tab index

            messagebox.showinfo("Success", "Prompt generated successfully!")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to generate prompt: {str(e)}")
    
    def save_prompt(self):
        """Save the current prompt."""
        if not self.current_prompt:
            messagebox.showwarning("Warning", "No prompt to save. Generate a prompt first.")
            return

        try:
            # Get save information from user
            title = self.song_title.get().strip() or "Untitled"
            description = self.song_description.get("1.0", tk.END).strip()
            genre = self.genre_var.get()

            # Create prompt data
            prompt_data = {
                "title": title,
                "description": description,
                "genre": genre,
                "prompt": self.current_prompt,
                "timestamp": datetime.now().isoformat(),
                "energy": self.energy_var.get(),
                "tempo": self.tempo_var.get(),
                "vocal_style": self.vocal_var.get(),
                "generate_lyrics": self.generate_lyrics_var.get(),
                "custom_lyrics": self.custom_lyrics.get("1.0", tk.END).strip() if not self.generate_lyrics_var.get() else ""
            }

            # Add to saved prompts
            self.saved_prompts.append(prompt_data)

            # Save to file
            self.save_prompts_to_file()

            messagebox.showinfo("Success", f"Prompt '{title}' saved successfully!")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save prompt: {str(e)}")

    def export_prompt(self):
        """Export the current prompt to a file."""
        if not self.current_prompt:
            messagebox.showwarning("Warning", "No prompt to export. Generate a prompt first.")
            return

        try:
            # Ask user for export format
            export_formats = [
                ("Text files", "*.txt"),
                ("JSON files", "*.json"),
                ("All files", "*.*")
            ]

            filename = filedialog.asksaveasfilename(
                title="Export Prompt",
                defaultextension=".txt",
                filetypes=export_formats
            )

            if filename:
                if filename.endswith('.json'):
                    # Export as JSON with metadata
                    export_data = {
                        "title": self.song_title.get().strip() or "Untitled",
                        "description": self.song_description.get("1.0", tk.END).strip(),
                        "genre": self.genre_var.get(),
                        "prompt": self.current_prompt,
                        "export_timestamp": datetime.now().isoformat(),
                        "generator_version": "1.0.0"
                    }

                    with open(filename, 'w', encoding='utf-8') as f:
                        json.dump(export_data, f, indent=2, ensure_ascii=False)
                else:
                    # Export as plain text
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(f"# SUNO AI Prompt Export\n")
                        f.write(f"# Title: {self.song_title.get().strip() or 'Untitled'}\n")
                        f.write(f"# Genre: {self.genre_var.get()}\n")
                        f.write(f"# Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                        f.write(self.current_prompt)

                messagebox.showinfo("Success", f"Prompt exported to {filename}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to export prompt: {str(e)}")
    
    def copy_to_clipboard(self):
        """Copy the current prompt to clipboard."""
        if not self.current_prompt:
            messagebox.showwarning("Warning", "No prompt to copy.")
            return
        
        self.root.clipboard_clear()
        self.root.clipboard_append(self.current_prompt)
        messagebox.showinfo("Success", "Prompt copied to clipboard!")
    
    def clear_all(self):
        """Clear all inputs and reset the application."""
        self.song_description.delete("1.0", tk.END)
        self.preview_text.delete("1.0", tk.END)
        self.current_prompt = ""
        self.genre_var.set("Modern Metalcore")
    
    def save_prompts_to_file(self):
        """Save prompts to a JSON file."""
        try:
            prompts_dir = os.path.join(os.path.expanduser("~"), ".suno_prompt_generator")
            os.makedirs(prompts_dir, exist_ok=True)

            prompts_file = os.path.join(prompts_dir, "saved_prompts.json")

            with open(prompts_file, 'w', encoding='utf-8') as f:
                json.dump(self.saved_prompts, f, indent=2, ensure_ascii=False)

        except Exception as e:
            print(f"Error saving prompts: {e}")

    def load_saved_prompts(self):
        """Load previously saved prompts."""
        try:
            prompts_dir = os.path.join(os.path.expanduser("~"), ".suno_prompt_generator")
            prompts_file = os.path.join(prompts_dir, "saved_prompts.json")

            if os.path.exists(prompts_file):
                with open(prompts_file, 'r', encoding='utf-8') as f:
                    self.saved_prompts = json.load(f)
            else:
                self.saved_prompts = []

        except Exception as e:
            print(f"Error loading prompts: {e}")
            self.saved_prompts = []

    def load_prompt_from_file(self):
        """Load a prompt from a file."""
        try:
            filetypes = [
                ("JSON files", "*.json"),
                ("Text files", "*.txt"),
                ("All files", "*.*")
            ]

            filename = filedialog.askopenfilename(
                title="Load Prompt",
                filetypes=filetypes
            )

            if filename:
                if filename.endswith('.json'):
                    with open(filename, 'r', encoding='utf-8') as f:
                        data = json.load(f)

                    # Load data into UI
                    if 'title' in data:
                        self.song_title.delete(0, tk.END)
                        self.song_title.insert(0, data['title'])

                    if 'description' in data:
                        self.song_description.delete("1.0", tk.END)
                        self.song_description.insert("1.0", data['description'])

                    if 'genre' in data:
                        self.genre_var.set(data['genre'])

                    if 'prompt' in data:
                        self.current_prompt = data['prompt']
                        self.preview_text.delete("1.0", tk.END)
                        self.preview_text.insert("1.0", self.current_prompt)

                    # Load advanced options if available
                    if 'energy' in data:
                        self.energy_var.set(data['energy'])
                    if 'tempo' in data:
                        self.tempo_var.set(data['tempo'])
                    if 'vocal_style' in data:
                        self.vocal_var.set(data['vocal_style'])

                else:
                    # Load as plain text
                    with open(filename, 'r', encoding='utf-8') as f:
                        content = f.read()

                    self.current_prompt = content
                    self.preview_text.delete("1.0", tk.END)
                    self.preview_text.insert("1.0", content)

                messagebox.showinfo("Success", "Prompt loaded successfully!")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to load prompt: {str(e)}")

    def refresh_template_list(self):
        """Refresh the template list."""
        self.template_listbox.delete(0, tk.END)
        templates = self.metal_templates.get_all_templates()
        for name in templates.keys():
            self.template_listbox.insert(tk.END, name)

    def on_template_select(self, event):
        """Handle template selection."""
        selection = self.template_listbox.curselection()
        if selection:
            template_name = self.template_listbox.get(selection[0])
            template = self.metal_templates.get_template(template_name)

            if template:
                # Display template details
                self.template_details.config(state=tk.NORMAL)
                self.template_details.delete("1.0", tk.END)

                details = f"Genre: {template.name}\n\n"
                details += f"Description: {template.description}\n\n"
                details += f"Typical Instruments:\n"
                for instrument in template.typical_instruments:
                    details += f"  • {instrument}\n"
                details += f"\nVocal Styles:\n"
                for vocal in template.vocal_styles:
                    details += f"  • {vocal}\n"
                details += f"\nMood Options:\n"
                for mood in template.mood_options:
                    details += f"  • {mood}\n"
                details += f"\nCharacteristic Elements:\n"
                for element in template.characteristic_elements:
                    details += f"  • {element}\n"
                details += f"\nExample Themes:\n"
                for theme in template.example_themes:
                    details += f"  • {theme}\n"

                self.template_details.insert("1.0", details)
                self.template_details.config(state=tk.DISABLED)

    def use_template(self):
        """Use the selected template to populate the form."""
        selection = self.template_listbox.curselection()
        if selection:
            template_name = self.template_listbox.get(selection[0])
            self.genre_var.set(template_name)
            self.on_genre_change()
            messagebox.showinfo("Success", f"Template '{template_name}' applied!")
        else:
            messagebox.showwarning("Warning", "Please select a template first.")

    def refresh_saved_list(self):
        """Refresh the saved prompts list."""
        # Clear existing items
        for item in self.saved_tree.get_children():
            self.saved_tree.delete(item)

        # Add saved prompts
        for i, prompt_data in enumerate(self.saved_prompts):
            title = prompt_data.get('title', 'Untitled')
            genre = prompt_data.get('genre', 'Unknown')
            timestamp = prompt_data.get('timestamp', '')

            # Format date
            try:
                if timestamp:
                    date_obj = datetime.fromisoformat(timestamp)
                    date_str = date_obj.strftime('%Y-%m-%d %H:%M')
                else:
                    date_str = 'Unknown'
            except:
                date_str = 'Unknown'

            self.saved_tree.insert('', tk.END, values=(title, genre, date_str), tags=(str(i),))

    def load_selected_prompt(self):
        """Load the selected saved prompt."""
        selection = self.saved_tree.selection()
        if selection:
            item = self.saved_tree.item(selection[0])
            index = int(item['tags'][0])

            if 0 <= index < len(self.saved_prompts):
                prompt_data = self.saved_prompts[index]

                # Load data into UI
                self.song_title.delete(0, tk.END)
                self.song_title.insert(0, prompt_data.get('title', ''))

                self.song_description.delete("1.0", tk.END)
                self.song_description.insert("1.0", prompt_data.get('description', ''))

                self.genre_var.set(prompt_data.get('genre', 'Modern Metalcore'))
                self.energy_var.set(prompt_data.get('energy', 'Auto'))
                self.tempo_var.set(prompt_data.get('tempo', 'Auto'))
                self.vocal_var.set(prompt_data.get('vocal_style', 'Auto'))

                self.generate_lyrics_var.set(prompt_data.get('generate_lyrics', True))
                self.on_lyrics_option_change()

                if not prompt_data.get('generate_lyrics', True):
                    self.custom_lyrics.delete("1.0", tk.END)
                    self.custom_lyrics.insert("1.0", prompt_data.get('custom_lyrics', ''))

                self.current_prompt = prompt_data.get('prompt', '')
                self.preview_text.delete("1.0", tk.END)
                self.preview_text.insert("1.0", self.current_prompt)

                messagebox.showinfo("Success", "Prompt loaded successfully!")
        else:
            messagebox.showwarning("Warning", "Please select a prompt to load.")

    def delete_selected_prompt(self):
        """Delete the selected saved prompt."""
        selection = self.saved_tree.selection()
        if selection:
            item = self.saved_tree.item(selection[0])
            index = int(item['tags'][0])

            if 0 <= index < len(self.saved_prompts):
                title = self.saved_prompts[index].get('title', 'Untitled')

                if messagebox.askyesno("Confirm Delete", f"Delete prompt '{title}'?"):
                    del self.saved_prompts[index]
                    self.save_prompts_to_file()
                    self.refresh_saved_list()
                    messagebox.showinfo("Success", "Prompt deleted successfully!")
        else:
            messagebox.showwarning("Warning", "Please select a prompt to delete.")


def main():
    """Main application entry point."""
    root = tk.Tk()
    app = SunoPromptApp(root)
    root.mainloop()


if __name__ == "__main__":
    main()
