"""
Lyric Generation and Enhancement Module
Generates and enhances lyrics for metal songs based on themes and genres.
"""

import random
import re
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass


@dataclass
class LyricSection:
    """Represents a section of lyrics."""
    section_type: str  # verse, chorus, bridge, etc.
    lines: List[str]
    rhyme_scheme: str = "ABAB"


@dataclass
class SongLyrics:
    """Complete song lyrics structure."""
    title: str
    sections: List[LyricSection]
    theme: str
    genre: str


class LyricGenerator:
    """Generates lyrics for metal songs based on themes and genres."""
    
    def __init__(self):
        self.metal_vocabulary = self._initialize_metal_vocabulary()
        self.rhyme_patterns = self._initialize_rhyme_patterns()
        self.theme_templates = self._initialize_theme_templates()
        
    def _initialize_metal_vocabulary(self) -> Dict[str, List[str]]:
        """Initialize metal-specific vocabulary by category."""
        return {
            "power_words": [
                "fire", "steel", "thunder", "lightning", "storm", "rage", "fury",
                "darkness", "shadow", "light", "flame", "iron", "blood", "war",
                "battle", "fight", "strength", "power", "force", "might", "will"
            ],
            "emotional_words": [
                "pain", "sorrow", "anguish", "despair", "hope", "fear", "anger",
                "love", "hate", "passion", "desire", "longing", "grief", "joy",
                "triumph", "victory", "defeat", "loss", "betrayal", "trust"
            ],
            "action_words": [
                "rise", "fall", "break", "shatter", "burn", "destroy", "create",
                "build", "tear", "rip", "crush", "strike", "stand", "fight",
                "run", "escape", "conquer", "overcome", "endure", "survive"
            ],
            "nature_words": [
                "mountain", "ocean", "sky", "earth", "wind", "rain", "snow",
                "forest", "desert", "river", "stone", "tree", "star", "moon",
                "sun", "night", "dawn", "dusk", "winter", "summer", "storm"
            ],
            "abstract_concepts": [
                "freedom", "chains", "prison", "cage", "path", "journey", "destiny",
                "fate", "time", "eternity", "infinity", "void", "abyss", "dream",
                "nightmare", "reality", "truth", "lies", "justice", "vengeance"
            ]
        }
    
    def _initialize_rhyme_patterns(self) -> Dict[str, List[str]]:
        """Initialize common rhyme patterns for different sections."""
        return {
            "verse": ["ABAB", "AABB", "ABCB"],
            "chorus": ["AABA", "ABAB", "AAAA"],
            "bridge": ["ABAB", "ABCB", "AABB"]
        }
    
    def _initialize_theme_templates(self) -> Dict[str, Dict]:
        """Initialize theme-based lyric templates."""
        return {
            "struggle": {
                "keywords": ["fight", "battle", "overcome", "strength", "endure"],
                "verse_starters": [
                    "In the depths of darkness",
                    "When the world comes crashing down",
                    "Through the pain and suffering",
                    "Against all odds we stand"
                ],
                "chorus_themes": [
                    "We will rise above",
                    "Never give up the fight",
                    "Strength through adversity",
                    "Breaking free from chains"
                ]
            },
            "loss": {
                "keywords": ["gone", "memory", "tears", "empty", "alone"],
                "verse_starters": [
                    "Empty halls echo with silence",
                    "Memories fade like autumn leaves",
                    "In the shadow of what was",
                    "Time cannot heal this wound"
                ],
                "chorus_themes": [
                    "You're gone but not forgotten",
                    "In my heart you'll always be",
                    "The pain will never fade",
                    "Lost but not alone"
                ]
            },
            "anger": {
                "keywords": ["rage", "fury", "burn", "destroy", "hate"],
                "verse_starters": [
                    "Fire burns within my veins",
                    "Rage consumes my very soul",
                    "The fury cannot be contained",
                    "Anger rises like a storm"
                ],
                "chorus_themes": [
                    "Let the fury take control",
                    "Burn it all to the ground",
                    "Rage against the dying light",
                    "Fury unleashed upon the world"
                ]
            },
            "hope": {
                "keywords": ["light", "dawn", "rise", "hope", "tomorrow"],
                "verse_starters": [
                    "Through the darkness comes the light",
                    "Dawn breaks upon the horizon",
                    "Hope springs eternal in the heart",
                    "Tomorrow brings a new beginning"
                ],
                "chorus_themes": [
                    "Hope will light the way",
                    "Dawn of a new day",
                    "Rise above the darkness",
                    "Light conquers all"
                ]
            },
            "rebellion": {
                "keywords": ["fight", "resist", "freedom", "break", "chains"],
                "verse_starters": [
                    "They try to keep us down",
                    "Chains of oppression bind us",
                    "The system tries to break us",
                    "We will not be silenced"
                ],
                "chorus_themes": [
                    "Break free from the chains",
                    "Fight for your freedom",
                    "Resist the oppression",
                    "Stand up and be counted"
                ]
            }
        }
    
    def analyze_theme(self, description: str) -> str:
        """Analyze description to determine the main theme."""
        description_lower = description.lower()
        
        theme_indicators = {
            "struggle": ["struggle", "fight", "battle", "overcome", "difficult", "hard"],
            "loss": ["loss", "death", "gone", "miss", "lost", "goodbye", "end"],
            "anger": ["angry", "mad", "rage", "fury", "hate", "pissed", "furious"],
            "hope": ["hope", "better", "future", "light", "positive", "tomorrow"],
            "rebellion": ["rebel", "fight", "system", "against", "resist", "freedom"],
            "love": ["love", "heart", "together", "relationship", "romance"],
            "betrayal": ["betray", "lie", "cheat", "trust", "broken", "deceive"]
        }
        
        theme_scores = {}
        for theme, indicators in theme_indicators.items():
            score = sum(1 for indicator in indicators if indicator in description_lower)
            if score > 0:
                theme_scores[theme] = score
        
        if theme_scores:
            return max(theme_scores, key=theme_scores.get)
        else:
            return "struggle"  # default theme
    
    def generate_rhyming_words(self, word: str) -> List[str]:
        """Generate simple rhyming words (basic implementation)."""
        # This is a simplified rhyme generator
        # In a production app, you'd use a proper rhyming dictionary
        
        rhyme_endings = {
            "ight": ["fight", "light", "night", "sight", "might", "right"],
            "ain": ["pain", "rain", "chain", "gain", "strain", "main"],
            "ire": ["fire", "desire", "tire", "wire", "inspire", "require"],
            "ound": ["sound", "ground", "found", "bound", "round", "wound"],
            "eart": ["heart", "start", "part", "art", "smart", "chart"],
            "ead": ["dead", "head", "red", "led", "fed", "said"],
            "ong": ["strong", "long", "song", "wrong", "belong", "along"],
            "ime": ["time", "rhyme", "climb", "prime", "crime", "sublime"]
        }
        
        word_lower = word.lower()
        for ending, rhymes in rhyme_endings.items():
            if word_lower.endswith(ending[-3:]):
                return [r for r in rhymes if r != word_lower]
        
        # Fallback: return some generic rhyming words
        return ["way", "day", "say", "play", "stay", "pray"]
    
    def generate_verse(self, theme: str, genre: str, line_count: int = 4) -> LyricSection:
        """Generate a verse based on theme and genre."""
        theme_config = self.theme_templates.get(theme, self.theme_templates["struggle"])
        
        lines = []
        starter = random.choice(theme_config["verse_starters"])
        lines.append(starter)
        
        # Generate additional lines
        keywords = theme_config["keywords"]
        vocabulary = self.metal_vocabulary
        
        for i in range(line_count - 1):
            # Create lines that incorporate theme keywords and metal vocabulary
            power_word = random.choice(vocabulary["power_words"])
            emotional_word = random.choice(vocabulary["emotional_words"])
            action_word = random.choice(vocabulary["action_words"])
            
            line_templates = [
                f"The {power_word} of {emotional_word} will {action_word}",
                f"Through {emotional_word} we {action_word} like {power_word}",
                f"When {power_word} meets {emotional_word}, we {action_word}",
                f"No {emotional_word} can stop this {power_word}"
            ]
            
            line = random.choice(line_templates)
            lines.append(line)
        
        return LyricSection("verse", lines, "ABAB")
    
    def generate_chorus(self, theme: str, genre: str, line_count: int = 4) -> LyricSection:
        """Generate a chorus based on theme and genre."""
        theme_config = self.theme_templates.get(theme, self.theme_templates["struggle"])
        
        lines = []
        main_theme = random.choice(theme_config["chorus_themes"])
        lines.append(main_theme)
        
        # Generate supporting lines
        keywords = theme_config["keywords"]
        vocabulary = self.metal_vocabulary
        
        for i in range(line_count - 1):
            if i == line_count - 2:  # Second to last line
                lines.append(main_theme)  # Repeat main theme
            else:
                power_word = random.choice(vocabulary["power_words"])
                emotional_word = random.choice(vocabulary["emotional_words"])
                
                supporting_templates = [
                    f"With {power_word} in our hearts",
                    f"Through {emotional_word} we find our way",
                    f"The {power_word} will set us free",
                    f"No more {emotional_word}, no more pain"
                ]
                
                line = random.choice(supporting_templates)
                lines.append(line)
        
        return LyricSection("chorus", lines, "AABA")
    
    def generate_bridge(self, theme: str, genre: str, line_count: int = 4) -> LyricSection:
        """Generate a bridge section."""
        vocabulary = self.metal_vocabulary
        
        lines = []
        
        # Bridge should provide contrast or build-up
        bridge_templates = [
            "In the silence before the storm",
            "When all hope seems lost",
            "At the edge of eternity",
            "In the heart of darkness",
            "Where shadows meet the light",
            "Beyond the veil of time"
        ]
        
        lines.append(random.choice(bridge_templates))
        
        for i in range(line_count - 1):
            abstract_word = random.choice(vocabulary["abstract_concepts"])
            nature_word = random.choice(vocabulary["nature_words"])
            action_word = random.choice(vocabulary["action_words"])
            
            line_templates = [
                f"The {nature_word} whispers of {abstract_word}",
                f"We {action_word} through {abstract_word}",
                f"Like {nature_word} in the {abstract_word}",
                f"The {abstract_word} calls us to {action_word}"
            ]
            
            line = random.choice(line_templates)
            lines.append(line)
        
        return LyricSection("bridge", lines, "ABAB")
    
    def generate_song_lyrics(self, description: str, genre: str, title: str = "") -> SongLyrics:
        """Generate complete song lyrics."""
        theme = self.analyze_theme(description)
        
        if not title:
            # Generate title based on theme
            theme_config = self.theme_templates.get(theme, self.theme_templates["struggle"])
            title_words = theme_config["keywords"][:2]
            title = " ".join(word.title() for word in title_words)
        
        sections = []
        
        # Generate song structure
        sections.append(self.generate_verse(theme, genre))
        sections.append(self.generate_chorus(theme, genre))
        sections.append(self.generate_verse(theme, genre))
        sections.append(self.generate_chorus(theme, genre))
        sections.append(self.generate_bridge(theme, genre))
        sections.append(self.generate_chorus(theme, genre))
        
        return SongLyrics(title, sections, theme, genre)
    
    def enhance_existing_lyrics(self, lyrics: str, genre: str) -> str:
        """Enhance existing lyrics with metal-specific vocabulary."""
        # Simple enhancement - replace generic words with metal vocabulary
        enhanced = lyrics
        
        replacements = {
            "good": random.choice(["powerful", "mighty", "strong"]),
            "bad": random.choice(["dark", "evil", "cursed"]),
            "big": random.choice(["massive", "colossal", "towering"]),
            "small": random.choice(["fragile", "broken", "shattered"]),
            "happy": random.choice(["triumphant", "victorious", "glorious"]),
            "sad": random.choice(["sorrowful", "anguished", "tormented"])
        }
        
        for old_word, new_word in replacements.items():
            enhanced = re.sub(r'\b' + old_word + r'\b', new_word, enhanced, flags=re.IGNORECASE)
        
        return enhanced
    
    def format_lyrics_for_suno(self, song_lyrics: SongLyrics) -> str:
        """Format lyrics for SUNO AI with proper structure tags."""
        formatted_lines = []
        
        for section in song_lyrics.sections:
            formatted_lines.append(f"[{section.section_type.title()}]")
            for line in section.lines:
                formatted_lines.append(line)
            formatted_lines.append("")  # Empty line between sections
        
        return "\n".join(formatted_lines)
