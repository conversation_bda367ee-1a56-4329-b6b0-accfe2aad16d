"""
SUNO AI Prompt Generator Engine
Core logic for generating SUNO AI prompts based on user input and genre templates.
"""

import re
import random
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass


@dataclass
class PromptComponents:
    """Data class to hold prompt components."""
    genre_tags: List[str]
    mood_tags: List[str]
    instrument_tags: List[str]
    vocal_tags: List[str]
    structure_tags: List[str]
    energy_level: str
    tempo: str
    lyrics: str = ""


class PromptGenerator:
    """Main prompt generation engine for SUNO AI."""
    
    def __init__(self):
        self.genre_mappings = self._initialize_genre_mappings()
        self.mood_keywords = self._initialize_mood_keywords()
        self.energy_levels = ["Low", "Medium", "High", "Extreme"]
        self.tempo_options = ["Slow", "Mid", "Fast", "Variable"]
        
    def _initialize_genre_mappings(self) -> Dict[str, Dict]:
        """Initialize genre-specific mappings for prompt generation."""
        return {
            "Modern Metalcore": {
                "base_tags": ["metalcore", "modern metal", "heavy"],
                "instruments": [
                    "Electric Guitar (Distorted)",
                    "Electric Guitar (Clean)",
                    "Bass Guitar (Heavy)",
                    "Drums (Heavy)",
                    "Synthesizer (Atmospheric)"
                ],
                "vocal_styles": ["screamed vocals", "clean vocals", "melodic vocals"],
                "typical_structure": ["Intro", "Verse", "Chorus", "Verse", "Chorus", "Bridge", "Breakdown", "Chorus", "Outro"],
                "mood_tendencies": ["aggressive", "emotional", "uplifting", "dark"],
                "tempo_range": ["Mid", "Fast"],
                "energy_range": ["Medium", "High", "Extreme"]
            },
            "Djent": {
                "base_tags": ["djent", "progressive metal", "polyrhythmic"],
                "instruments": [
                    "Electric Guitar (Palm Muted)",
                    "Electric Guitar (7-String)",
                    "Bass Guitar (Extended Range)",
                    "Drums (Complex)",
                    "Synthesizer (Ambient)"
                ],
                "vocal_styles": ["clean vocals", "harsh vocals", "ambient vocals"],
                "typical_structure": ["Intro", "Verse", "Pre-Chorus", "Chorus", "Verse", "Chorus", "Bridge", "Solo", "Chorus", "Outro"],
                "mood_tendencies": ["atmospheric", "technical", "progressive", "introspective"],
                "tempo_range": ["Mid", "Variable"],
                "energy_range": ["Medium", "High"]
            },
            "Melodic Death Metal": {
                "base_tags": ["melodic death metal", "death metal", "melodic"],
                "instruments": [
                    "Electric Guitar (Melodic Lead)",
                    "Electric Guitar (Rhythm)",
                    "Bass Guitar (Aggressive)",
                    "Drums (Blast Beats)",
                    "Keyboard (Orchestral)"
                ],
                "vocal_styles": ["death growls", "melodic clean vocals", "harsh vocals"],
                "typical_structure": ["Intro", "Verse", "Chorus", "Verse", "Chorus", "Solo", "Bridge", "Chorus", "Outro"],
                "mood_tendencies": ["melodic", "aggressive", "epic", "dark"],
                "tempo_range": ["Fast"],
                "energy_range": ["High", "Extreme"]
            },
            "Progressive Metal": {
                "base_tags": ["progressive metal", "progressive", "complex"],
                "instruments": [
                    "Electric Guitar (Technical)",
                    "Bass Guitar (Complex)",
                    "Drums (Technical)",
                    "Keyboard (Progressive)",
                    "Synthesizer (Atmospheric)"
                ],
                "vocal_styles": ["progressive vocals", "clean vocals", "operatic vocals"],
                "typical_structure": ["Intro", "Verse", "Chorus", "Verse", "Chorus", "Instrumental", "Bridge", "Solo", "Chorus", "Outro"],
                "mood_tendencies": ["progressive", "complex", "atmospheric", "epic"],
                "tempo_range": ["Variable"],
                "energy_range": ["Medium", "High"]
            },
            "Technical Death Metal": {
                "base_tags": ["technical death metal", "death metal", "technical"],
                "instruments": [
                    "Electric Guitar (Technical)",
                    "Electric Guitar (Sweep Picking)",
                    "Bass Guitar (Technical)",
                    "Drums (Technical)",
                    "Synthesizer (Minimal)"
                ],
                "vocal_styles": ["death growls", "technical vocals", "brutal vocals"],
                "typical_structure": ["Intro", "Verse", "Chorus", "Verse", "Chorus", "Technical Solo", "Bridge", "Chorus", "Outro"],
                "mood_tendencies": ["technical", "aggressive", "complex", "brutal"],
                "tempo_range": ["Fast"],
                "energy_range": ["High", "Extreme"]
            }
        }
    
    def _initialize_mood_keywords(self) -> Dict[str, List[str]]:
        """Initialize mood keyword mappings."""
        return {
            "sad": ["melancholic", "sorrowful", "emotional", "dark"],
            "angry": ["aggressive", "furious", "intense", "brutal"],
            "happy": ["uplifting", "energetic", "positive", "triumphant"],
            "energetic": ["high energy", "powerful", "driving", "intense"],
            "dark": ["dark", "ominous", "atmospheric", "haunting"],
            "epic": ["epic", "grandiose", "majestic", "powerful"],
            "emotional": ["emotional", "heartfelt", "passionate", "moving"],
            "aggressive": ["aggressive", "brutal", "fierce", "intense"],
            "atmospheric": ["atmospheric", "ambient", "ethereal", "spacious"],
            "technical": ["technical", "complex", "intricate", "precise"]
        }
    
    def analyze_description(self, description: str) -> Dict[str, any]:
        """Analyze user description to extract mood, themes, and characteristics."""
        description_lower = description.lower()
        
        # Extract mood indicators
        detected_moods = []
        for mood, keywords in self.mood_keywords.items():
            if mood in description_lower or any(keyword in description_lower for keyword in keywords):
                detected_moods.append(mood)
        
        # Extract energy level indicators
        energy_indicators = {
            "low": ["slow", "calm", "peaceful", "quiet", "soft"],
            "medium": ["moderate", "balanced", "steady"],
            "high": ["fast", "energetic", "powerful", "intense"],
            "extreme": ["brutal", "extreme", "crushing", "devastating"]
        }
        
        detected_energy = "Medium"  # default
        for level, indicators in energy_indicators.items():
            if any(indicator in description_lower for indicator in indicators):
                detected_energy = level.capitalize()
                break
        
        # Extract tempo indicators
        tempo_indicators = {
            "slow": ["slow", "ballad", "calm"],
            "mid": ["moderate", "medium", "steady"],
            "fast": ["fast", "quick", "rapid", "speed"],
            "variable": ["changing", "dynamic", "varied"]
        }
        
        detected_tempo = "Mid"  # default
        for tempo, indicators in tempo_indicators.items():
            if any(indicator in description_lower for indicator in indicators):
                detected_tempo = tempo.capitalize()
                break
        
        # Extract theme keywords
        themes = []
        theme_patterns = [
            r'\b(love|heartbreak|loss|death|war|battle|victory|defeat)\b',
            r'\b(struggle|pain|hope|despair|anger|rage|fury)\b',
            r'\b(freedom|rebellion|resistance|fight|strength)\b'
        ]
        
        for pattern in theme_patterns:
            matches = re.findall(pattern, description_lower)
            themes.extend(matches)
        
        return {
            "moods": detected_moods,
            "energy": detected_energy,
            "tempo": detected_tempo,
            "themes": themes,
            "original_description": description
        }
    
    def generate_prompt_components(self, description: str, genre: str) -> PromptComponents:
        """Generate prompt components based on description and genre."""
        analysis = self.analyze_description(description)
        genre_config = self.genre_mappings.get(genre, self.genre_mappings["Modern Metalcore"])
        
        # Generate genre tags
        genre_tags = genre_config["base_tags"].copy()
        
        # Generate mood tags based on analysis
        mood_tags = []
        for mood in analysis["moods"]:
            if mood in genre_config["mood_tendencies"]:
                mood_tags.append(f"Mood: {mood}")
        
        # If no moods detected, use genre defaults
        if not mood_tags:
            default_mood = random.choice(genre_config["mood_tendencies"])
            mood_tags.append(f"Mood: {default_mood}")
        
        # Generate instrument tags
        instrument_tags = []
        # Select 3-4 instruments from genre config
        selected_instruments = random.sample(
            genre_config["instruments"], 
            min(4, len(genre_config["instruments"]))
        )
        for instrument in selected_instruments:
            instrument_tags.append(f"Instrument: {instrument}")
        
        # Generate vocal tags
        vocal_tags = []
        selected_vocal_style = random.choice(genre_config["vocal_styles"])
        vocal_tags.append(f"Vocalist: {selected_vocal_style}")
        
        # Generate structure tags
        structure_tags = genre_config["typical_structure"].copy()
        
        # Determine energy and tempo
        energy = analysis["energy"]
        if energy not in genre_config["energy_range"]:
            energy = random.choice(genre_config["energy_range"])
        
        tempo = analysis["tempo"]
        if tempo not in genre_config["tempo_range"]:
            tempo = random.choice(genre_config["tempo_range"])
        
        return PromptComponents(
            genre_tags=genre_tags,
            mood_tags=mood_tags,
            instrument_tags=instrument_tags,
            vocal_tags=vocal_tags,
            structure_tags=structure_tags,
            energy_level=energy,
            tempo=tempo
        )
    
    def format_prompt(self, components: PromptComponents, description: str) -> str:
        """Format the final SUNO AI prompt."""
        prompt_lines = []
        
        # Add header comment
        prompt_lines.append("# SUNO AI Prompt - Generated by Metal Prompt Generator")
        prompt_lines.append("")
        
        # Add genre and style tags
        prompt_lines.append("[Intro]")
        for tag in components.genre_tags:
            prompt_lines.append(f"[Genre: {tag}]")
        
        # Add mood and energy
        for mood_tag in components.mood_tags:
            prompt_lines.append(f"[{mood_tag}]")
        
        prompt_lines.append(f"[Energy: {components.energy_level}]")
        prompt_lines.append(f"[Tempo: {components.tempo}]")
        
        # Add instrument tags
        for instrument_tag in components.instrument_tags:
            prompt_lines.append(f"[{instrument_tag}]")
        
        # Add vocal tags
        for vocal_tag in components.vocal_tags:
            prompt_lines.append(f"[{vocal_tag}]")
        
        prompt_lines.append("")
        
        # Add structure with placeholder lyrics
        for i, section in enumerate(components.structure_tags):
            prompt_lines.append(f"[{section}]")
            
            if section.lower() in ["verse", "chorus", "bridge"]:
                # Add placeholder lyrics based on description
                if "verse" in section.lower():
                    prompt_lines.append("# Verse lyrics based on: " + description)
                    prompt_lines.append("(Verse lyrics will be generated here)")
                elif "chorus" in section.lower():
                    prompt_lines.append("# Chorus - main hook")
                    prompt_lines.append("(Chorus lyrics will be generated here)")
                elif "bridge" in section.lower():
                    prompt_lines.append("# Bridge - contrasting section")
                    prompt_lines.append("(Bridge lyrics will be generated here)")
            
            prompt_lines.append("")
        
        return "\n".join(prompt_lines)
    
    def generate_advanced(self, description: str, genre: str, title: str = "",
                         lyrics: str = "", energy: str = None, tempo: str = None,
                         vocal_style: str = None) -> str:
        """Advanced method to generate a SUNO AI prompt with custom options."""
        if not description.strip():
            raise ValueError("Description cannot be empty")

        if genre not in self.genre_mappings:
            raise ValueError(f"Unsupported genre: {genre}")

        # Generate base prompt components
        components = self.generate_prompt_components(description, genre)

        # Override with custom options if provided
        if energy:
            components.energy_level = energy
        if tempo:
            components.tempo = tempo
        if vocal_style:
            # Update vocal tags based on custom style
            if vocal_style == "Clean Vocals":
                components.vocal_tags = ["Vocalist: clean vocals"]
            elif vocal_style == "Harsh Vocals":
                components.vocal_tags = ["Vocalist: harsh vocals"]
            elif vocal_style == "Mixed Vocals":
                components.vocal_tags = ["Vocalist: clean vocals", "Vocalist: harsh vocals"]
            elif vocal_style == "Instrumental":
                components.vocal_tags = []

        # Add custom lyrics if provided
        if lyrics:
            components.lyrics = lyrics

        # Format and return the final prompt
        return self.format_advanced_prompt(components, description, title, lyrics)

    def format_advanced_prompt(self, components: PromptComponents, description: str,
                              title: str = "", lyrics: str = "") -> str:
        """Format an advanced SUNO AI prompt with custom lyrics and options."""
        prompt_lines = []

        # Add header comment
        prompt_lines.append("# SUNO AI Prompt - Generated by Metal Prompt Generator")
        if title:
            prompt_lines.append(f"# Title: {title}")
        prompt_lines.append(f"# Description: {description}")
        prompt_lines.append("")

        # Add genre and style tags
        prompt_lines.append("[Intro]")
        for tag in components.genre_tags:
            prompt_lines.append(f"[Genre: {tag}]")

        # Add mood and energy
        for mood_tag in components.mood_tags:
            prompt_lines.append(f"[{mood_tag}]")

        prompt_lines.append(f"[Energy: {components.energy_level}]")
        prompt_lines.append(f"[Tempo: {components.tempo}]")

        # Add instrument tags
        for instrument_tag in components.instrument_tags:
            prompt_lines.append(f"[{instrument_tag}]")

        # Add vocal tags
        for vocal_tag in components.vocal_tags:
            prompt_lines.append(f"[{vocal_tag}]")

        prompt_lines.append("")

        # Add lyrics if provided, otherwise use structure with placeholders
        if lyrics:
            prompt_lines.append(lyrics)
        else:
            # Add structure with placeholder lyrics
            for section in components.structure_tags:
                prompt_lines.append(f"[{section}]")

                if section.lower() in ["verse", "chorus", "bridge"]:
                    if "verse" in section.lower():
                        prompt_lines.append("# Verse lyrics based on: " + description)
                        prompt_lines.append("(Verse lyrics will be generated here)")
                    elif "chorus" in section.lower():
                        prompt_lines.append("# Chorus - main hook")
                        prompt_lines.append("(Chorus lyrics will be generated here)")
                    elif "bridge" in section.lower():
                        prompt_lines.append("# Bridge - contrasting section")
                        prompt_lines.append("(Bridge lyrics will be generated here)")

                prompt_lines.append("")

        return "\n".join(prompt_lines)

    def generate(self, description: str, genre: str) -> str:
        """Main method to generate a complete SUNO AI prompt."""
        return self.generate_advanced(description, genre)
