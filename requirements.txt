# SUNO AI Prompt Generator Requirements
# Python GUI application for generating SUNO AI prompts for metal music

# Core dependencies (built into Python)
# tkinter - GUI framework (included with Python)
# json - JSON handling (included with Python)
# os - Operating system interface (included with Python)
# datetime - Date and time handling (included with Python)
# typing - Type hints (included with Python 3.5+)
# dataclasses - Data classes (included with Python 3.7+)
# re - Regular expressions (included with Python)
# random - Random number generation (included with Python)

# Optional dependencies for enhanced functionality
# Uncomment and install if you want additional features:

# For enhanced text processing and NLP features:
# nltk>=3.8
# textblob>=0.17.1

# For advanced lyric generation using AI APIs:
# openai>=1.0.0
# anthropic>=0.7.0

# For audio file handling (if adding audio preview features):
# pydub>=0.25.1
# pygame>=2.1.0

# For web scraping SUNO documentation updates:
# requests>=2.28.0
# beautifulsoup4>=4.11.0

# For enhanced GUI themes and styling:
# ttkthemes>=3.2.2
# pillow>=9.0.0

# For database storage of prompts (alternative to JSON):
# sqlite3 (included with Python)

# Development and testing dependencies:
# pytest>=7.0.0
# pytest-cov>=4.0.0
# black>=22.0.0
# flake8>=5.0.0

# Note: This application is designed to work with Python's built-in libraries
# to ensure maximum compatibility and ease of installation.
# All core functionality works without additional dependencies.
