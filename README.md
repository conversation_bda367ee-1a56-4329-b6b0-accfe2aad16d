# SUNO AI Prompt Generator - Metal Edition

A comprehensive Python GUI application for generating complex SUNO AI prompts specifically optimized for modern metal genres. Transform simple song concepts into detailed, professional prompts with genre-specific terminology, song structures, and automatically generated lyrics.

## Features

### 🎸 **Metal Genre Specialization**
- **Modern Metalcore**: Heavy breakdowns, clean/screamed vocal contrast, melodic elements
- **Djent**: Polyrhythmic patterns, palm-muted riffs, complex time signatures  
- **Melodic Death Metal**: Melodic guitar harmonies, blast beats, epic orchestrations
- **Progressive Metal**: Complex compositions, technical proficiency, dynamic changes
- **Technical Death Metal**: Virtuosic playing, unusual time signatures, precision

### 🎵 **Intelligent Prompt Generation**
- Analyzes user descriptions to detect mood, energy, and themes
- Generates genre-appropriate SUNO AI meta tags and structure
- Includes proper `[Genre]`, `[Mood]`, `[Energy]`, `[Instrument]`, and `[Vocalist]` tags
- Follows SUNO AI v4.5 prompt structure best practices

### 📝 **Lyric Generation & Enhancement**
- Automatic lyric generation based on themes and genres
- Metal-specific vocabulary and terminology
- Theme-based templates (struggle, loss, anger, hope, rebellion)
- Support for custom lyrics with enhancement suggestions

### 💾 **Save & Export System**
- Save prompts with full metadata for later use
- Export to text or JSON formats
- Load and modify existing prompts
- Template management system

### 🎨 **User-Friendly Interface**
- Dark metal-themed GUI design
- Tabbed interface for organized workflow
- Real-time preview of generated prompts
- Advanced options for fine-tuning

## Installation

### Prerequisites
- Python 3.7 or higher
- tkinter (included with most Python installations)

### Quick Start
1. **Clone or download** this repository
2. **Navigate** to the project directory
3. **Run** the application:
   ```bash
   python main.py
   ```

### Optional Dependencies
For enhanced features, install optional dependencies:
```bash
pip install -r requirements.txt
```

## Usage Guide

### Basic Workflow

1. **Launch the Application**
   ```bash
   python main.py
   ```

2. **Enter Song Concept**
   - Describe your song idea in simple terms
   - Example: "aggressive breakup song with heavy breakdowns"

3. **Select Metal Genre**
   - Choose from 5 specialized metal subgenres
   - Each genre has optimized templates and terminology

4. **Configure Advanced Options** (Optional)
   - Energy Level: Low, Medium, High, Extreme
   - Tempo: Slow, Mid, Fast, Variable
   - Vocal Style: Clean, Harsh, Mixed, Instrumental

5. **Generate Lyrics** (Optional)
   - Auto-generate theme-based lyrics
   - Or provide your own custom lyrics

6. **Generate Prompt**
   - Click "Generate Prompt" to create your SUNO AI prompt
   - Preview the result in the Preview tab

7. **Save & Export**
   - Save prompts for later use
   - Export to text or JSON formats
   - Copy to clipboard for direct use in SUNO AI

### Example Input/Output

**Input:**
- Description: "Epic battle song with soaring melodies and powerful vocals"
- Genre: Melodic Death Metal
- Energy: High

**Generated Output:**
```
# SUNO AI Prompt - Generated by Metal Prompt Generator
# Title: Epic Battle
# Description: Epic battle song with soaring melodies and powerful vocals

[Intro]
[Genre: melodic death metal]
[Genre: death metal]
[Genre: melodic]
[Mood: epic]
[Energy: High]
[Tempo: Fast]
[Instrument: Electric Guitar (Melodic Lead)]
[Instrument: Electric Guitar (Harmonized)]
[Instrument: Bass Guitar (Aggressive)]
[Instrument: Drums (Blast Beats)]
[Vocalist: death growls]

[Verse]
Through the darkness comes the light
The steel of power will rise
When fire meets triumph, we conquer
No sorrow can stop this might

[Chorus]
Rise above the darkness
With steel in our hearts
The fire will set us free
Rise above the darkness

[Bridge]
In the heart of darkness
The mountain whispers of destiny
We fight through freedom
Like storm in the eternity
```

## File Structure

```
suno-prompt-generator/
├── main.py                 # Main application GUI
├── prompt_generator.py     # Core prompt generation engine
├── metal_templates.py      # Metal genre templates and configurations
├── lyric_generator.py      # Lyric generation and enhancement
├── requirements.txt        # Dependencies (mostly optional)
├── README.md              # This file
└── examples/              # Example prompts and templates
```

## Advanced Features

### Template System
- Built-in templates for each metal subgenre
- Customizable genre configurations
- Template import/export functionality

### Lyric Generation
- Theme-based lyric templates
- Metal-specific vocabulary database
- Rhyme scheme support
- Enhancement of existing lyrics

### Save System
- Automatic saving to user directory
- JSON format with full metadata
- Cross-session persistence
- Backup and restore functionality

## Customization

### Adding Custom Genres
Modify `metal_templates.py` to add new genres:

```python
templates["Your Genre"] = MetalTemplate(
    name="Your Genre",
    description="Description of your genre",
    genre_tags=["tag1", "tag2"],
    typical_instruments=["instrument1", "instrument2"],
    # ... other properties
)
```

### Custom Vocabulary
Extend the vocabulary in `lyric_generator.py`:

```python
self.metal_vocabulary["your_category"] = [
    "word1", "word2", "word3"
]
```

## Troubleshooting

### Common Issues

**Application won't start:**
- Ensure Python 3.7+ is installed
- Check that tkinter is available: `python -c "import tkinter"`

**Prompts not saving:**
- Check write permissions in your home directory
- Ensure `~/.suno_prompt_generator/` directory can be created

**GUI appears broken:**
- Try updating your Python installation
- On Linux, install tkinter: `sudo apt-get install python3-tk`

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is open source. Feel free to use, modify, and distribute according to your needs.

## Acknowledgments

- SUNO AI team for their amazing music generation platform
- Metal music community for inspiration and feedback
- Python tkinter community for GUI development resources

## Support

For issues, questions, or feature requests, please create an issue in the repository or contact the development team.

---

**Generate epic metal prompts and unleash your creativity with SUNO AI! 🤘**
